/**
 * Supplier Profile Page
 *
 * This page displays comprehensive supplier information with multiple tabs/sections,
 * following the exact design pattern and styling used in UserEditPage and UserDetails.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import PageHeader from '../components/layout/PageHeader';
import Tabs from '../components/common/Tabs';
import LoadingSpinner from '../components/common/LoadingSpinner';
import Button from '../components/common/Button';
import Modal from '../components/common/Modal';
import useNotification from '../hooks/useNotification';
import { TrashIcon, NoSymbolIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import SupplierPersonalInfo from '../features/suppliers/components/SupplierPersonalInfo';
// TEMPORARILY DISABLED: Documents and Analytics tabs
// import SupplierDocuments from '../features/suppliers/components/SupplierDocuments';
import SupplierProducts from '../features/suppliers/components/SupplierProducts';
// import SupplierAnalytics from '../features/suppliers/components/SupplierAnalytics';
import { useSuppliers } from '../features/suppliers/hooks/useSuppliers';
import type {
  Supplier,
  SupplierProduct,
  // TEMPORARILY DISABLED: Documents and Analytics tabs
  // SupplierDocument,
  // SupplierAnalyticsData
} from '../features/suppliers/types';
import { ROUTES } from '../constants/routes';



const SupplierProfilePage: React.FC = () => {
  const { id: supplierId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showError, showSuccess } = useNotification();

  // Use refs to store stable references to notification functions
  const showErrorRef = useRef(showError);
  const showSuccessRef = useRef(showSuccess);
  const isMountedRef = useRef(true);

  // Update refs when functions change
  useEffect(() => {
    showErrorRef.current = showError;
    showSuccessRef.current = showSuccess;
  }, [showError, showSuccess]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);
  
  // State
  // TEMPORARILY DISABLED: Documents and Analytics tabs - simplified tab state
  const [activeTab, setActiveTab] = useState<'personal' | 'products'>('personal');
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [supplierProducts, setSupplierProducts] = useState<SupplierProduct[]>([]);
  // TEMPORARILY DISABLED: Documents and Analytics tabs
  // const [supplierDocuments, setSupplierDocuments] = useState<SupplierDocument[]>([]);
  // const [supplierAnalytics, setSupplierAnalytics] = useState<SupplierAnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isBanModalOpen, setIsBanModalOpen] = useState(false);
  const [isBanning, setIsBanning] = useState(false);
  // TEMPORARILY DISABLED: Documents and Analytics tabs
  // const [documentsAvailable, setDocumentsAvailable] = useState(true);
  // const [analyticsAvailable, setAnalyticsAvailable] = useState(true);

  // Use the useSuppliers hook for API integration
  const {
    getSupplierById,
    getSupplierProducts,
    // TEMPORARILY DISABLED: Documents and Analytics tabs
    // getSupplierDocuments,
    // getSupplierAnalytics,
    deleteSupplier,
    banSupplier,
    unbanSupplier,
    isLoading: hookLoading
  } = useSuppliers();



  // Fetch supplier data - optimized to prevent duplicate requests
  const fetchSupplierData = useCallback(async () => {
    if (!supplierId) {
      setError('No supplier ID provided');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Fetch supplier data from API (required)
      const supplierData = await getSupplierById(supplierId);

      if (!isMountedRef.current) return; // Prevent state updates if component unmounted
      setSupplier(supplierData);

      // Fetch products data (required)
      const products = await getSupplierProducts(supplierId);

      if (!isMountedRef.current) return;
      setSupplierProducts(products);

      // TEMPORARILY DISABLED: Documents and Analytics tabs
      // Fetch optional data (documents and analytics) - these may return empty/null for 404s
      // const [documents, analytics] = await Promise.all([
      //   getSupplierDocuments(supplierId),
      //   getSupplierAnalytics(supplierId)
      // ]);

      // if (!isMountedRef.current) return;

      // Handle documents response
      // setSupplierDocuments(documents || []);
      // setDocumentsAvailable(documents && documents.length > 0);

      // Handle analytics response
      // setSupplierAnalytics(analytics);
      // setAnalyticsAvailable(analytics !== null);

    } catch (error) {
      if (!isMountedRef.current) return;

      console.error('Error fetching supplier data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch supplier data';
      setError(errorMessage);
      showErrorRef.current('Failed to load supplier data');
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
    }
  }, [supplierId, getSupplierById, getSupplierProducts]); // TEMPORARILY DISABLED: getSupplierDocuments, getSupplierAnalytics

  // Effect to fetch data when supplierId changes
  useEffect(() => {
    fetchSupplierData();
  }, [fetchSupplierData]);

  // TEMPORARILY DISABLED: Documents and Analytics tabs
  // Effect to handle tab switching when endpoints become unavailable
  // useEffect(() => {
  //   if (activeTab === 'documents' && !documentsAvailable) {
  //     setActiveTab('personal');
  //   } else if (activeTab === 'analytics' && !analyticsAvailable) {
  //     setActiveTab('personal');
  //   }
  // }, [activeTab, documentsAvailable, analyticsAvailable]);



  // Handle supplier deletion
  const handleDeleteSupplier = async () => {
    if (!supplier) return;

    try {
      setIsDeleting(true);

      await deleteSupplier(supplier.id);

      showSuccessRef.current(`Supplier "${supplier.name}" has been deleted successfully`);
      setIsDeleteModalOpen(false);

      // Navigate back to suppliers list
      navigate(ROUTES.SUPPLIERS);

    } catch (error) {
      console.error('Error deleting supplier:', error);
      showErrorRef.current('Failed to delete supplier');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle supplier ban
  const handleBanSupplier = async () => {
    if (!supplier) return;

    try {
      setIsBanning(true);

      await banSupplier(supplier.id);

      showSuccessRef.current(`Supplier "${supplier.name}" has been banned successfully`);
      setIsBanModalOpen(false);

      // Refresh supplier data to show updated status
      await fetchSupplierData();

    } catch (error) {
      console.error('Error banning supplier:', error);
      showErrorRef.current('Failed to ban supplier');
    } finally {
      setIsBanning(false);
    }
  };

  // Handle supplier unban
  const handleUnbanSupplier = async () => {
    if (!supplier) return;

    try {
      setIsBanning(true);

      await unbanSupplier(supplier.id);

      showSuccessRef.current(`Supplier "${supplier.name}" has been unbanned successfully`);

      // Refresh supplier data to show updated status
      await fetchSupplierData();

    } catch (error) {
      console.error('Error unbanning supplier:', error);
      showErrorRef.current('Failed to unban supplier');
    } finally {
      setIsBanning(false);
    }
  };

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    // TEMPORARILY DISABLED: Documents and Analytics tabs - simplified tab handling
    const typedTabId = tabId as 'personal' | 'products';

    // TEMPORARILY DISABLED: Documents and Analytics tabs
    // Prevent switching to disabled tabs
    // if (typedTabId === 'documents' && !documentsAvailable) return;
    // if (typedTabId === 'analytics' && !analyticsAvailable) return;

    setActiveTab(typedTabId);
  };

  // Loading state
  if (isLoading || hookLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Error state
  if (error || !supplier) {
    return (
      <div className="space-y-6">
        <PageHeader
          title="Supplier Profile"
          description="Supplier not found"
          breadcrumbs={[
            { label: 'Suppliers', path: ROUTES.SUPPLIERS },
            { label: 'Profile' }
          ]}
        />
        <div className="text-center py-12">
          <p className="text-gray-500">{error || 'Supplier not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={`Supplier: ${supplier.name}`}
        description="Comprehensive supplier profile and management"
        breadcrumbs={[
          { label: 'Suppliers', path: ROUTES.SUPPLIERS },
          { label: supplier.name }
        ]}
        actions={
          <div className="flex gap-2">
            {supplier.status === 'banned' ? (
              <Button
                variant="success"
                size="sm"
                onClick={handleUnbanSupplier}
                icon={<CheckCircleIcon className="h-4 w-4" />}
                disabled={isLoading || isBanning || isDeleting}
                loading={isBanning}
              >
                Unban Supplier
              </Button>
            ) : (
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setIsBanModalOpen(true)}
                icon={<NoSymbolIcon className="h-4 w-4" />}
                disabled={isLoading || isBanning || isDeleting}
              >
                Ban Supplier
              </Button>
            )}
            <Button
              variant="danger"
              size="sm"
              onClick={() => setIsDeleteModalOpen(true)}
              icon={<TrashIcon className="h-4 w-4" />}
              disabled={isLoading || isDeleting || isBanning}
            >
              Delete Supplier
            </Button>
          </div>
        }
      />
      
      <Tabs
        tabs={[
          { id: 'personal', label: 'Personal Information' },
          // TEMPORARILY DISABLED: Documents and Analytics tabs
          // {
          //   id: 'documents',
          //   label: documentsAvailable ? 'Verification Documents' : 'Documents (Coming Soon)',
          //   disabled: !documentsAvailable
          // },
          { id: 'products', label: 'Products' },
          // {
          //   id: 'analytics',
          //   label: analyticsAvailable ? 'Analytics' : 'Analytics (Coming Soon)',
          //   disabled: !analyticsAvailable
          // }
        ]}
        activeTab={activeTab}
        onChange={handleTabChange}
      />
      
      {activeTab === 'personal' && (
        <SupplierPersonalInfo supplier={supplier} />
      )}

      {/* TEMPORARILY DISABLED: Documents tab content */}
      {/* {activeTab === 'documents' && (
        documentsAvailable ? (
          <SupplierDocuments
            documents={supplierDocuments}
            supplierId={supplier.id}
          />
        ) : (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Documents Coming Soon</h3>
            <p className="text-gray-500">The verification documents feature is currently under development and will be available soon.</p>
          </div>
        )
      )} */}

      {activeTab === 'products' && (
        <SupplierProducts
          products={supplierProducts}
          supplierId={supplier.id}
          onProductUpdate={fetchSupplierData}
        />
      )}

      {/* TEMPORARILY DISABLED: Analytics tab content */}
      {/* {activeTab === 'analytics' && (
        analyticsAvailable && supplierAnalytics ? (
          <SupplierAnalytics
            supplierData={supplierAnalytics}
            supplierId={supplier.id}
          />
        ) : (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Coming Soon</h3>
            <p className="text-gray-500">The analytics dashboard is currently under development and will be available soon.</p>
          </div>
        )
      )} */

      {/* Ban Confirmation Modal */}
      <Modal
        isOpen={isBanModalOpen}
        onClose={() => setIsBanModalOpen(false)}
        title="Ban Supplier"
        size="sm"
        footer={
          <>
            <Button
              variant="outline"
              onClick={() => setIsBanModalOpen(false)}
              disabled={isBanning}
            >
              Cancel
            </Button>
            <Button
              variant="secondary"
              onClick={handleBanSupplier}
              loading={isBanning}
              icon={<NoSymbolIcon className="h-4 w-4" />}
            >
              Ban Supplier
            </Button>
          </>
        }
      >
        <div className="text-sm text-gray-500">
          <p className="mb-3">
            Are you sure you want to ban <strong>"{supplier.name}"</strong>?
          </p>
          <p className="text-orange-600 font-medium">
            This action will:
          </p>
          <ul className="mt-2 list-disc list-inside text-orange-600">
            <li>Change the supplier's status to 'banned'</li>
            <li>Prevent them from receiving new orders</li>
            <li>Restrict their access to the platform</li>
            <li>Allow for potential future reactivation</li>
          </ul>
          <p className="mt-3 text-gray-600">
            Unlike deletion, this action can be reversed by changing the supplier's status back to 'active'.
          </p>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Supplier"
        size="sm"
        footer={
          <>
            <Button
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteSupplier}
              loading={isDeleting}
              icon={<TrashIcon className="h-4 w-4" />}
            >
              Delete Supplier
            </Button>
          </>
        }
      >
        <div className="text-sm text-gray-500">
          <p className="mb-3">
            Are you sure you want to delete <strong>"{supplier.name}"</strong>?
          </p>
          <p className="text-red-600 font-medium">
            This action cannot be undone and will permanently remove:
          </p>
          <ul className="mt-2 list-disc list-inside text-red-600">
            <li>All supplier information</li>
            <li>Associated products and documents</li>
            <li>Order history and analytics</li>
          </ul>
        </div>
      </Modal>
    </div>
  );
};

export default SupplierProfilePage;
